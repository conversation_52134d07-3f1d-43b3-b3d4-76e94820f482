import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { UserRole } from '../types/User';
import { Transaction } from '../types/Transaction';

// Screens
import DashboardScreen from '../screens/dashboard/DashboardScreen';
import TransactionListScreen from '../screens/transactions/TransactionListScreen';
import CreateTransactionScreen from '../screens/transactions/CreateTransactionScreen';
import TransactionDetailScreen from '../screens/transactions/TransactionDetailScreen';
import ApprovalListScreen from '../screens/approvals/ApprovalListScreen';
import EditTransactionScreen from '../screens/transactions/EditTransactionScreen';

export type MainStackParamList = {
  Dashboard: undefined;
  TransactionList: { transactions?: Transaction[] };
  CreateTransaction: { type?: string };
  TransactionDetail: { transactionId: string };
  ApprovalList: { pendingTransactions?: Transaction[] };
  EditTransaction: { transaction: any };
};

const Stack = createStackNavigator<MainStackParamList>();

export default function MainNavigator() {
  return (
    <Stack.Navigator
      id={undefined}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="Dashboard"
        component={DashboardScreen}
      />
      <Stack.Screen
        name="TransactionList"
        component={TransactionListScreen}
      />
      <Stack.Screen
        name="CreateTransaction"
        component={CreateTransactionScreen}
      />
      <Stack.Screen
        name="TransactionDetail"
        component={TransactionDetailScreen}
      />
      <Stack.Screen
        name="ApprovalList"
        component={ApprovalListScreen}
      />
      <Stack.Screen
        name="EditTransaction"
        component={EditTransactionScreen}
      />
    </Stack.Navigator>
  );
}
