import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { format } from 'date-fns';
import { Transaction, TransactionType } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/currency';
import StatusBadge from '../common/StatusBadge';

interface TransactionCardProps {
  transaction: Transaction;
  onPress?: () => void;
  showUser?: boolean;
}

export default function TransactionCard({ 
  transaction, 
  onPress,
  showUser = false 
}: TransactionCardProps) {
  const getTransactionIcon = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return '💰';
      case TransactionType.SPEND:
        return '💳';
      case TransactionType.RETURNED:
        return '↩️';
      default:
        return '💸';
    }
  };

  const getTransactionTitle = (transaction: Transaction) => {
    switch (transaction.type) {
      case TransactionType.TRANSFER:
        return `Cash given to ${transaction.recipient || 'Unknown'}`;
      case TransactionType.SPEND:
        return transaction.description || 'Expense';
      case TransactionType.RETURNED:
        return transaction.description || 'Cash returned';
      default:
        return 'Transaction';
    }
  };

  const getAmountColor = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return Colors.warning;
      case TransactionType.SPEND:
        return Colors.error;
      case TransactionType.RETURNED:
        return Colors.success;
      default:
        return Colors.textPrimary;
    }
  };

  const getAmountPrefix = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
      case TransactionType.SPEND:
        return '-';
      case TransactionType.RETURNED:
        return '+';
      default:
        return '';
    }
  };

  return (
    <TouchableOpacity
      style={styles.card}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>{getTransactionIcon(transaction.type)}</Text>
        </View>
        <View style={styles.content}>
          <Text style={styles.title} numberOfLines={2}>
            {getTransactionTitle(transaction)}
          </Text>
          <View style={styles.details}>
            <Text style={styles.glCode}>{transaction.glCode}</Text>
            <Text style={styles.separator}>•</Text>
            <Text style={styles.programCode}>{transaction.programCode}</Text>
          </View>
          {transaction.purpose && (
            <Text style={styles.purpose} numberOfLines={1}>
              {transaction.purpose}
            </Text>
          )}
        </View>
        <View style={styles.rightSection}>
          <Text style={[
            styles.amount,
            { color: getAmountColor(transaction.type) }
          ]}>
            {getAmountPrefix(transaction.type)}{formatCurrency(Number(transaction.amount), transaction.currency)}
          </Text>
          <StatusBadge status={transaction.status} size="small" />
        </View>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.date}>
          {format(new Date(transaction.createdAt), 'MMM dd, yyyy • HH:mm')}
        </Text>
        {showUser && (
          <Text style={styles.user}>
            by User {transaction.createdBy}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 16,
    marginVertical: 4,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  icon: {
    fontSize: 20,
  },
  content: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  details: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  glCode: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  separator: {
    fontSize: 12,
    color: Colors.textTertiary,
    marginHorizontal: 6,
  },
  programCode: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  purpose: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.gray100,
  },
  date: {
    fontSize: 12,
    color: Colors.textTertiary,
  },
  user: {
    fontSize: 12,
    color: Colors.textTertiary,
    fontStyle: 'italic',
  },
});
