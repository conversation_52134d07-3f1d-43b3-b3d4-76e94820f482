import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import { format } from 'date-fns';
import { RootState } from '../../store/store';
import { TransactionType, TransactionStatus } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { formatCurrency } from '../../utils/currency';
import StatusBadge from '../../components/common/StatusBadge';
import Button from '../../components/common/Button';
import ZoomableImage from '../../components/common/ZoomableImage';

export default function TransactionDetailScreen() {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { user } = useSelector((state: RootState) => state.auth);
  const transaction = route.params?.transaction;
  const { users: allUsers } = useSelector((state: RootState) => state.users);

  if (!user || !transaction) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Transaction Not Found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
          />
        </View>
      </View>
    );
  }

  // Adapt to new transaction object format
  const createdByUser = transaction.createdBy || (transaction.user ? `${transaction.user.first_name} ${transaction.user.last_name}` : transaction.created_by);
  const approvedByUser = transaction.approvedBy || transaction.approved_by || null;
  const recipientUser = transaction.recipient || transaction.related_user || null;
  const glCode = transaction.glCode || transaction.gl_code?.code || '';
  const glName = transaction.glName || transaction.gl_code?.name || '';
  const programCode = transaction.programCode || transaction.program_code?.code || '';
  const programName = transaction.programName || transaction.program_code?.name || '';
  const rejectionReason = transaction.rejectionReason || transaction.rejected_reason || null;

  const getTransactionIcon = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return '\ud83d\udcb0';
      case TransactionType.SPEND:
        return '\ud83d\udcb3';
      case TransactionType.RETURNED:
        return '\u21a9\ufe0f';
      case TransactionType.DEPOSIT:
        return '\ud83d\udcb5'; // money with wings emoji for deposit
      default:
        return '\ud83d\udcb8';
    }
  };

  const getTransactionTitle = () => {
    switch (transaction.type) {
      case TransactionType.TRANSFER:
        return 'Cash Given';
      case TransactionType.SPEND:
        return 'Cash Spent';
      case TransactionType.RETURNED:
        return 'Cash Returned';
      case TransactionType.DEPOSIT:
        return 'Deposit';
      default:
        return 'Transaction';
    }
  };

  const getAmountColor = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return Colors.warning;
      case TransactionType.SPEND:
        return Colors.error;
      case TransactionType.RETURNED:
        return Colors.success;
      case TransactionType.DEPOSIT:
        return Colors.success;
      default:
        return Colors.textPrimary;
    }
  };

  const getAmountPrefix = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
      case TransactionType.SPEND:
        return '-';
      case TransactionType.RETURNED:
      case TransactionType.DEPOSIT:
        return '+';
      default:
        return '';
    }
  };

  // Helper to get display names for splitWith
  const getSplitWithDisplay = () => {
    if (!transaction.splitWith || transaction.splitWith.length === 0) return null;
    // Try to match by id or email
    const displayNames = transaction.splitWith.map((idOrEmail) => {
      const user = allUsers.find(
        (u) => u.id === idOrEmail || u.email === idOrEmail
      );
      if (user) {
        const fullName = user.first_name && user.last_name
          ? `${user.first_name} ${user.last_name}`.trim()
          : user.name || '';
        return fullName ? `${fullName} (${user.email})` : user.email;
      }
      // fallback to showing the id/email
      return idOrEmail;
    });
    return displayNames.join('\n');
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Transaction Details</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Transaction Overview */}
        <View style={styles.card}>
          <View style={styles.transactionHeader}>
            <View style={styles.iconContainer}>
              <Text style={styles.icon}>{getTransactionIcon(transaction.type)}</Text>
            </View>
            <View style={styles.transactionInfo}>
              <Text style={styles.transactionTitle}>{getTransactionTitle()}</Text>
              <StatusBadge status={transaction.status} />
            </View>
            <Text style={[
              styles.amount,
              { color: getAmountColor(transaction.type) }
            ]}>
              {getAmountPrefix(transaction.type)}{formatCurrency(Number(transaction.amount), transaction.currency)}
            </Text>
          </View>
        </View>

        {/* Transaction Details */}
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>Details</Text>
          
          {transaction.description && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Description</Text>
              <Text style={styles.detailValue}>{transaction.description}</Text>
            </View>
          )}

          {transaction.recipient && recipientUser && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Recipient</Text>
              <Text style={styles.detailValue}>{recipientUser || ''}</Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>GL Code</Text>
            <Text style={styles.detailValue}>{glCode || glName || transaction.gl_code?.code || transaction.gl_code?.name || '—'}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Program Code</Text>
            <Text style={styles.detailValue}>{programCode || programName || transaction.program_code?.code || transaction.program_code?.name || '—'}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Created By</Text>
            <Text style={styles.detailValue}>
              {createdByUser || 'Unknown'}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Created At</Text>
            <Text style={styles.detailValue}>
              {format(new Date(transaction.createdAt), 'MMM dd, yyyy • HH:mm')}
            </Text>
          </View>

          {/* Split With section */}
          {transaction.splitWith && transaction.splitWith.length > 0 && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Split With</Text>
              <Text style={styles.detailValue}>
                {getSplitWithDisplay()}
              </Text>
            </View>
          )}

          {/* Show Approved By only for approved transactions */}
          {transaction.status === TransactionStatus.APPROVED && approvedByUser && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Approved By</Text>
              <Text style={styles.detailValue}>{approvedByUser}</Text>
            </View>
          )}

          {/* Show Rejected By and Rejection Reason only for rejected transactions */}
          {transaction.status === TransactionStatus.REJECTED && approvedByUser && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Rejected By</Text>
              <Text style={styles.detailValue}>{approvedByUser}</Text>
            </View>
          )}
          {transaction.status === TransactionStatus.REJECTED && rejectionReason && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Rejection Reason</Text>
              <Text style={[styles.detailValue, { color: Colors.error }]}>{rejectionReason}</Text>
            </View>
          )}
        </View>

        {/* Receipt */}
        {(transaction.receiptUrl || transaction.receiptUri) && (
          <View style={styles.card}>
            <Text style={styles.sectionTitle}>Receipt</Text>
            <ZoomableImage 
              uri={transaction.receiptUrl || transaction.receiptUri} 
              style={styles.receiptImage}
            />
          </View>
        )}
        {/* Edit Button for Pending and Draft Transactions */}
        {(transaction.status === TransactionStatus.PENDING || transaction.status === TransactionStatus.DRAFT) && (
          <View style={{ marginBottom: 24 }}>
            <Button
              title="Edit"
              onPress={() => navigation.navigate('EditTransaction', { transaction })}
              variant="primary"
            />
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.surface,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.background,
  },
  backButton: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  placeholder: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  icon: {
    fontSize: 30,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  amount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  detailRow: {
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
    paddingBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  receiptImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 24,
  },
});
