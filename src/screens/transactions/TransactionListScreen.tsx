import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { useSelector } from 'react-redux';
import { useNavigation, useIsFocused, useRoute } from '@react-navigation/native';
import { RootState } from '../../store/store';
import { TransactionType } from '../../types/Transaction';
import { Colors } from '../../constants/colors';
import { canCreateTransaction } from '../../utils/permissions';
import { formatCurrency } from '../../utils/currency';
import StatusBadge from '../../components/common/StatusBadge';
import Button from '../../components/common/Button';
import { apiService } from '../../services/apiService';

type StatusFilterType = 'all' | 'pending' | 'approved' | 'rejected' | 'draft' | 'cancelled';
type TypeFilterType = 'all' | 'spend' | 'transfer' | 'returned' | 'deposit';

export default function TransactionListScreen() {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [statusFilter, setStatusFilter] = useState<StatusFilterType>('all');
  const [typeFilter, setTypeFilter] = useState<TypeFilterType>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showTypeDropdown, setShowTypeDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const isFocused = useIsFocused();

  // Get transactions from navigation params if available
  const navigationTransactions = route.params?.transactions;

  if (!user) return null;
  
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const apiTransactions = await apiService.getTransactionHistory();
        setTransactions(apiTransactions);
      } catch (error) {
        setTransactions([]);
      } finally {
        setLoading(false);
      }
    };

    // Use navigation params if available, otherwise fetch from API
    if (navigationTransactions && navigationTransactions.length >= 0) {
      setTransactions(navigationTransactions);
      setLoading(false);
    } else if (isFocused) {
      fetchTransactions();
    }
  }, [isFocused, navigationTransactions]);

  // Apply filters
  const filteredTransactions = transactions.filter(transaction => {
    // Status filter
    if (statusFilter !== 'all' && transaction.status !== statusFilter) return false;

    // Type filter
    if (typeFilter !== 'all' && transaction.type !== typeFilter) return false;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const description = transaction.description?.toLowerCase() || '';
      const recipient = transaction.recipient?.toLowerCase() || '';
      const amount = transaction.amount.toString();

      return description.includes(query) ||
             recipient.includes(query) ||
             amount.includes(query);
    }

    return true;
  });

  const sortedTransactions = filteredTransactions.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const getTransactionIcon = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return '\ud83d\udcb0';
      case TransactionType.SPEND:
        return '\ud83d\udcb3';
      case TransactionType.RETURNED:
        return '\u21a9\ufe0f';
      case TransactionType.DEPOSIT:
        return '\ud83d\udcb5'; // money with wings emoji for deposit
      default:
        return '\ud83d\udcb8';
    }
  };

  const getTransactionType = (transaction: any) => {
    switch (transaction.type) {
      case TransactionType.SPEND:
        return 'Cash Spent';
      case TransactionType.TRANSFER:
        return 'Cash Given';
      case TransactionType.RETURNED:
        return 'Cash Returned';
      case TransactionType.DEPOSIT:
        return 'Deposit';
      default:
        return 'Transaction';
    }
  };

  const getAmountColor = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
        return Colors.warning;
      case TransactionType.SPEND:
        return Colors.error;
      case TransactionType.RETURNED:
        return Colors.success;
      case TransactionType.DEPOSIT:
        return Colors.success;
      default:
        return Colors.textPrimary;
    }
  };

  const getAmountPrefix = (type: TransactionType) => {
    switch (type) {
      case TransactionType.TRANSFER:
      case TransactionType.SPEND:
        return '-';
      case TransactionType.RETURNED:
      case TransactionType.DEPOSIT:
        return '+';
      default:
        return '';
    }
  };

  const getRecipientOrSelf = (transaction: any) => {
    if (transaction.type === TransactionType.TRANSFER && transaction.recipient) {
      return transaction.recipient;
    }
    return 'Self';
  };

  const getTypeFilterLabel = (type: TypeFilterType) => {
    switch (type) {
      case 'spend':
        return 'Cash Spent';
      case 'transfer':
        return 'Cash Given';
      case 'returned':
        return 'Cash Returned';
      case 'deposit':
        return 'Deposit';
      default:
        return 'All Types';
    }
  };

  const getStatusFilterLabel = (status: StatusFilterType) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      case 'draft':
        return 'Draft';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'All Status';
    }
  };

  const typeOptions: { value: TypeFilterType; label: string }[] = [
    { value: 'all', label: 'All Types' },
    { value: 'spend', label: 'Cash Spent' },
    { value: 'transfer', label: 'Cash Given' },
    { value: 'returned', label: 'Cash Returned' },
    { value: 'deposit', label: 'Deposit' },
  ];

  const statusOptions: { value: StatusFilterType; label: string }[] = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'draft', label: 'Draft' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  const renderTransaction = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.transactionCard}
      onPress={() => navigation.navigate('TransactionDetail', { transaction: item })}
      activeOpacity={0.7}
    >
      <View style={styles.cardContent}>
        <View style={styles.iconContainer}>
          <Text style={styles.transactionIcon}>{getTransactionIcon(item.type)}</Text>
        </View>

        <View style={styles.transactionInfo}>
          <View style={styles.transactionHeader}>
            <Text style={styles.transactionType}>{getTransactionType(item)}</Text>
            <Text style={[
              styles.transactionAmount,
              { color: getAmountColor(item.type) }
            ]}>
              {getAmountPrefix(item.type)}{formatCurrency(Number(item.amount), item.currency)}
            </Text>
          </View>

          <View style={styles.transactionDetails}>
            <Text style={styles.recipient}>{getRecipientOrSelf(item)}</Text>
            <StatusBadge status={item.status} size="small" />
          </View>

          <View style={styles.transactionFooter}>
            <Text style={styles.description}>{item.description}</Text>
            <Text style={styles.date}>
              {new Date(item.createdAt).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              })}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    loading ? (
      <View style={styles.emptyState}>
        <Text style={styles.emptyIcon}>⏳</Text>
        <Text style={styles.emptyTitle}>Loading transactions...</Text>
      </View>
    ) : (
      <View style={styles.emptyState}>
        <Text style={styles.emptyIcon}>📝</Text>
        <Text style={styles.emptyTitle}>No transactions found</Text>
        <Text style={styles.emptySubtitle}>
          {statusFilter === 'all' && typeFilter === 'all' && !searchQuery
            ? 'Create your first transaction to get started'
            : 'No transactions match your filters'
          }
        </Text>
        {statusFilter === 'all' && typeFilter === 'all' && !searchQuery && canCreateTransaction(user.role, TransactionType.SPEND) && (
          <Button
            title="Create Transaction"
            onPress={() => navigation.navigate('CreateTransaction')}
            style={styles.emptyButton}
          />
        )}
      </View>
    )
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backArrow}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>My Transactions</Text>
        <TouchableOpacity
          style={styles.filterIconButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Text style={styles.filterIcon}>
            {showFilters ? '✖️' : '⚙️'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Filter Dropdowns */}
      {showFilters && (
        <View style={styles.filtersContainer}>
          {/* Type Filter Dropdown */}
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={() => {
                setShowTypeDropdown(!showTypeDropdown);
                setShowStatusDropdown(false);
              }}
            >
              <Text style={styles.dropdownText}>{getTypeFilterLabel(typeFilter)}</Text>
              <Text style={styles.dropdownArrow}>▼</Text>
            </TouchableOpacity>

            {showTypeDropdown && (
              <View style={styles.dropdownMenu}>
                {typeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.dropdownItem,
                      typeFilter === option.value && styles.dropdownItemSelected
                    ]}
                    onPress={() => {
                      setTypeFilter(option.value);
                      setShowTypeDropdown(false);
                    }}
                  >
                    <Text style={[
                      styles.dropdownItemText,
                      typeFilter === option.value && styles.dropdownItemTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          {/* Status Filter Dropdown */}
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={() => {
                setShowStatusDropdown(!showStatusDropdown);
                setShowTypeDropdown(false);
              }}
            >
              <Text style={styles.dropdownText}>{getStatusFilterLabel(statusFilter)}</Text>
              <Text style={styles.dropdownArrow}>▼</Text>
            </TouchableOpacity>

            {showStatusDropdown && (
              <View style={styles.dropdownMenu}>
                {statusOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.dropdownItem,
                      statusFilter === option.value && styles.dropdownItemSelected
                    ]}
                    onPress={() => {
                      setStatusFilter(option.value);
                      setShowStatusDropdown(false);
                    }}
                  >
                    <Text style={[
                      styles.dropdownItemText,
                      statusFilter === option.value && styles.dropdownItemTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        </View>
      )}

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchInput}
          placeholder="Search transactions..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={Colors.textTertiary}
          onFocus={() => {
            setShowTypeDropdown(false);
            setShowStatusDropdown(false);
          }}
        />
      </View>

      {/* Transactions List */}
      <FlatList
        data={sortedTransactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={renderEmptyState}
        onScrollBeginDrag={() => {
          setShowTypeDropdown(false);
          setShowStatusDropdown(false);
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.gray50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: Colors.background,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backArrow: {
    fontSize: 24,
    color: Colors.textPrimary,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  filterIconButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterIcon: {
    fontSize: 20,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.background,
    gap: 12,
  },
  dropdownContainer: {
    flex: 1,
    position: 'relative',
  },
  dropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.gray50,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  dropdownText: {
    fontSize: 16,
    color: Colors.textPrimary,
  },
  dropdownArrow: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: Colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.gray200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
    marginTop: 4,
  },
  dropdownItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  dropdownItemSelected: {
    backgroundColor: Colors.primary + '10',
  },
  dropdownItemText: {
    fontSize: 16,
    color: Colors.textPrimary,
  },
  dropdownItemTextSelected: {
    color: Colors.primary,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  searchIcon: {
    fontSize: 16,
    marginRight: 12,
    color: Colors.textSecondary,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  listContainer: {
    paddingVertical: 8,
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    minWidth: 200,
  },
  // Transaction Card Styles
  transactionCard: {
    backgroundColor: Colors.background,
    marginHorizontal: 20,
    marginVertical: 4,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionIcon: {
    fontSize: 25,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  transactionType: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  transactionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recipient: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  description: {
    fontSize: 14,
    color: Colors.textSecondary,
    flex: 1,
    marginRight: 8,
  },
  date: {
    fontSize: 14,
    color: Colors.textTertiary,
  },
});
